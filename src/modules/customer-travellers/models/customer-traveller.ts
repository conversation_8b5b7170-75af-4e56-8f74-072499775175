import { model } from "@camped-ai/framework/utils";

export enum Gender {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}

export enum Relationship {
  SPOUSE = "spouse",
  CHILD = "child",
  SIBLING = "sibling",
  GRANDPARENT = "grandparent",
  FRIEND = "friend",
  PARENT = "parent",
  COLLEAGUE = "colleague",
  RELATIVE = "relative",
  PARENT_IN_LAW = "parent_in_law",
  OTHER = "other",
}

export const CustomerTraveller = model
  .define("customer_traveller", {
    id: model.id({ prefix: "cust_trav" }).primaryKey(),
    customer_id: model.text(),
    cart_id: model.text().nullable(),
    first_name: model.text(),
    last_name: model.text(),
    date_of_birth: model.dateTime(),
    gender: model.enum(Gender).nullable(),
    relationship: model.enum(Relationship).nullable(),
    is_primary: model.boolean().default(false),
  })
  .indexes([
    {
      name: "IDX_customer_traveller_customer_id",
      on: ["customer_id"],
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_customer_traveller_cart_id",
      on: ["cart_id"],
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_customer_traveller_customer_primary",
      on: ["customer_id", "is_primary"],
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_customer_traveller_name",
      on: ["customer_id", "first_name", "last_name"],
      where: "deleted_at IS NULL",
    },
  ]);
