import { MedusaError, MedusaErrorTypes } from "@camped-ai/utils";
import { EntityManager } from "@mikro-orm/postgresql";
import { Relationship } from "./models/customer-traveller";

// Service operates against the existing model() API entity "customer_traveller" table.
// This version uses TypeORM QueryBuilder (no raw SQL strings) to follow Medusa standards.

export type CreateTravellerInput = {
  customer_id: string;
  cart_id?: string | null;
  first_name: string;
  last_name: string;
  date_of_birth: string; // ISO string
  gender?: "male" | "female" | "other";
  relationship?: Relationship;
  is_primary?: boolean;
};

export type UpdateTravellerInput = Partial<Omit<CreateTravellerInput, "customer_id">> & {
  id: string;
};

const TABLE = "customer_traveller";

export default class CustomerTravellerService {
  private readonly manager: EntityManager;

  static readonly DEFAULT_MAX_TRAVELLERS = 10; // 1 primary + 9 additional

  constructor(deps: { manager: <PERSON>tity<PERSON>anager }) {
    this.manager = deps.manager;
  }

  private qb(manager: EntityManager) {
    // MikroORM qb: from raw table via qb(TABLE) and alias it as TABLE
    return manager.createQueryBuilder().select("*").from(TABLE).as(TABLE) as any;
  }

  private async listAllByCustomerTx(tx: EntityManager, customer_id: string) {
    const qb = tx.createQueryBuilder(TABLE);
    const rows = await qb
      .select("*")
      .where({ customer_id })
      .andWhere({ deleted_at: null })
      .orderBy({ created_at: "asc" })
      .execute();
    return rows;
  }

  async listByCustomer(customer_id: string) {
    const qb = this.manager.createQueryBuilder(TABLE);
    return await qb
      .select("*")
      .where({ customer_id })
      .andWhere({ deleted_at: null })
      .orderBy({ created_at: "asc" })
      .execute();
  }

  // Medusa-standard: service method to list travellers scoped to a specific cart
  async listByCart(cart_id: string) {
    const qb = this.manager.createQueryBuilder(TABLE);
    return await qb
      .select("*")
      .where({ cart_id })
      .andWhere({ deleted_at: null })
      .orderBy({ created_at: "asc" })
      .execute();
  }

  async getByIdForCustomer(id: string, customer_id: string) {
    const qb = this.manager.createQueryBuilder(TABLE);
    const rows = await qb
      .select("*")
      .where({ id, customer_id, deleted_at: null })
      .limit(1)
      .execute();
    const row = rows?.[0];

    if (!row) {
      throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
    }
    return row;
  }

  private async assertConstraints(input: CreateTravellerInput, tx: EntityManager) {
    const profiles = await this.listAllByCustomerTx(tx, input.customer_id);

    if (profiles.length >= CustomerTravellerService.DEFAULT_MAX_TRAVELLERS) {
      throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Maximum number of traveller profiles reached");
    }

    const is_primary = !!input.is_primary;

    if (is_primary) {
      const existingPrimary = profiles.find((p: any) => p.is_primary);
      if (existingPrimary) {
        throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
      }
    } else {
      // For additional travellers, relationship is required
      if (!input.relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }
    }
  }

  async create(input: CreateTravellerInput) {
    return await this.manager.getConnection().execute(async (conn) => {
      const tx = this.manager.fork({ clear: false });
      await this.assertConstraints(input, tx);

      // Generate id via database function using QueryBuilder insert with returning
      const rows = await tx
        .createQueryBuilder(TABLE)
        .insert({
          id: tx.raw(`concat('cust_trav_', replace(gen_random_uuid()::text, '-', ''))`),
          customer_id: input.customer_id,
          first_name: input.first_name,
          last_name: input.last_name,
          gender: input.gender ?? null,
          date_of_birth: new Date(input.date_of_birth) as any,
          relationship: (input.relationship ?? null) as any,
          is_primary: input.is_primary ?? false,
          cart_id: input.cart_id ?? null,
          created_at: tx.raw("now()"),
          updated_at: tx.raw("now()"),
        })
        .returning("*")
        .execute();
      return rows?.[0];
    });
  }

  async update(customer_id: string, id: string, input: UpdateTravellerInput) {
    return await this.manager.getConnection().execute(async (conn) => {
      const tx = this.manager.fork({ clear: false });
      const existingRows = await tx
        .createQueryBuilder(TABLE)
        .select("*")
        .where({ id, customer_id, deleted_at: null })
        .limit(1)
        .execute();
      const existing = existingRows?.[0];

      if (!existing) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
      }

      // If making this primary, ensure uniqueness
      if (input.is_primary === true && !existing.is_primary) {
        const cntRows = await tx
          .createQueryBuilder(TABLE)
          .select(tx.raw('COUNT(1) as c'))
          .where({ customer_id, is_primary: true, deleted_at: null })
          .execute();
        const hasPrimary = Number(cntRows?.[0]?.c ?? 0) > 0;
        if (hasPrimary) {
          throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
        }
      }

      // For non-primary additional travellers, ensure relationship exists
      const next_is_primary = input.is_primary ?? existing.is_primary;
      const next_relationship = input.relationship ?? existing.relationship;

      if (!next_is_primary && !next_relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }

      const rows = await tx
        .createQueryBuilder(TABLE)
        .update({
          first_name: input.first_name ?? existing.first_name,
          last_name: input.last_name ?? existing.last_name,
          gender: (input.gender ?? existing.gender) as any,
          date_of_birth: input.date_of_birth ? (new Date(input.date_of_birth) as any) : existing.date_of_birth,
          relationship: (next_relationship ?? null) as any,
          is_primary: next_is_primary,
          cart_id: input.cart_id ?? existing.cart_id,
          updated_at: tx.raw("now()"),
        })
        .where({ id, customer_id, deleted_at: null })
        .returning("*")
        .execute();

      return rows?.[0];
    });
  }

  async delete(customer_id: string, id: string): Promise<void> {
    await this.manager.getConnection().execute(async (conn) => {
      const tx = this.manager.fork({ clear: false });
      await tx
        .createQueryBuilder(TABLE)
        .update({
          deleted_at: tx.raw("now()"),
          updated_at: tx.raw("now()"),
        })
        .where({ id, customer_id, deleted_at: null })
        .execute();
    });
  }
}
