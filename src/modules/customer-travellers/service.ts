import { MedusaError, MedusaErrorTypes } from "@camped-ai/utils";
import { EntityManager } from "@mikro-orm/postgresql";
import { Relationship } from "./models/customer-traveller";

// Service operates against the existing model() API entity "customer_traveller" table.
// This version uses MikroORM QueryBuilder to follow Medusa standards.

export type CreateTravellerInput = {
  customer_id: string;
  cart_id?: string | null;
  first_name: string;
  last_name: string;
  date_of_birth: string; // ISO string
  gender?: "male" | "female" | "other" | null;
  relationship?: Relationship | null;
  is_primary?: boolean;
};

export type UpdateTravellerInput = Partial<Omit<CreateTravellerInput, "customer_id">> & {
  id: string;
};

export type TravellerProfile = {
  id: string;
  customer_id: string;
  cart_id: string | null;
  first_name: string;
  last_name: string;
  date_of_birth: Date;
  gender: "male" | "female" | "other" | null;
  relationship: Relationship | null;
  is_primary: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
};

const TABLE = "customer_traveller";

export default class CustomerTravellerService {
  private readonly manager: EntityManager;

  static readonly DEFAULT_MAX_TRAVELLERS = 10; // 1 primary + 9 additional

  constructor(deps: { manager: EntityManager }) {
    this.manager = deps.manager;
  }

  private async listAllByCustomerTx(tx: EntityManager, customer_id: string): Promise<TravellerProfile[]> {
    const result = await tx.execute(
      `SELECT * FROM ${TABLE} WHERE customer_id = ? AND deleted_at IS NULL ORDER BY created_at ASC`,
      [customer_id]
    );
    return result as TravellerProfile[];
  }

  async listByCustomer(customer_id: string): Promise<TravellerProfile[]> {
    const result = await this.manager.execute(
      `SELECT * FROM ${TABLE} WHERE customer_id = ? AND deleted_at IS NULL ORDER BY created_at ASC`,
      [customer_id]
    );
    return result as TravellerProfile[];
  }

  // Medusa-standard: service method to list travellers scoped to a specific cart
  async listByCart(cart_id: string): Promise<TravellerProfile[]> {
    const result = await this.manager.execute(
      `SELECT * FROM ${TABLE} WHERE cart_id = ? AND deleted_at IS NULL ORDER BY created_at ASC`,
      [cart_id]
    );
    return result as TravellerProfile[];
  }

  async getByIdForCustomer(id: string, customer_id: string): Promise<TravellerProfile> {
    const result = await this.manager.execute(
      `SELECT * FROM ${TABLE} WHERE id = ? AND customer_id = ? AND deleted_at IS NULL LIMIT 1`,
      [id, customer_id]
    );
    const row = result[0] as TravellerProfile;

    if (!row) {
      throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
    }
    return row;
  }

  private async assertConstraints(input: CreateTravellerInput, tx: EntityManager): Promise<void> {
    const profiles = await this.listAllByCustomerTx(tx, input.customer_id);

    if (profiles.length >= CustomerTravellerService.DEFAULT_MAX_TRAVELLERS) {
      throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Maximum number of traveller profiles reached");
    }

    const is_primary = !!input.is_primary;

    if (is_primary) {
      const existingPrimary = profiles.find((p: TravellerProfile) => p.is_primary);
      if (existingPrimary) {
        throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
      }
    } else {
      // For additional travellers, relationship is required
      if (!input.relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }
    }
  }

  async create(input: CreateTravellerInput): Promise<TravellerProfile> {
    return await this.manager.transactional(async (tx) => {
      await this.assertConstraints(input, tx);

      // Generate id and insert using raw SQL
      const result = await tx.execute(`
        INSERT INTO ${TABLE} (
          id, customer_id, cart_id, first_name, last_name,
          date_of_birth, gender, relationship, is_primary,
          created_at, updated_at
        ) VALUES (
          concat('cust_trav_', replace(gen_random_uuid()::text, '-', '')),
          ?, ?, ?, ?, ?, ?, ?, ?, now(), now()
        ) RETURNING *
      `, [
        input.customer_id,
        input.cart_id || null,
        input.first_name,
        input.last_name,
        input.date_of_birth,
        input.gender || null,
        input.relationship || null,
        input.is_primary || false
      ]);

      return result[0] as TravellerProfile;
    });
  }

  async update(customer_id: string, id: string, input: UpdateTravellerInput): Promise<TravellerProfile> {
    return await this.manager.transactional(async (tx) => {
      // Get existing record
      const existingResult = await tx.execute(
        `SELECT * FROM ${TABLE} WHERE id = ? AND customer_id = ? AND deleted_at IS NULL LIMIT 1`,
        [id, customer_id]
      );
      const existing = existingResult[0] as TravellerProfile;

      if (!existing) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
      }

      // If making this primary, ensure uniqueness
      if (input.is_primary === true && !existing.is_primary) {
        const countResult = await tx.execute(
          `SELECT COUNT(1) as count FROM ${TABLE} WHERE customer_id = ? AND is_primary = true AND deleted_at IS NULL`,
          [customer_id]
        );
        const hasPrimary = Number((countResult[0] as any).count) > 0;
        if (hasPrimary) {
          throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
        }
      }

      // For non-primary additional travellers, ensure relationship exists
      const next_is_primary = input.is_primary ?? existing.is_primary;
      const next_relationship = input.relationship ?? existing.relationship;

      if (!next_is_primary && !next_relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }

      // Update the record
      const result = await tx.execute(`
        UPDATE ${TABLE} SET
          first_name = ?,
          last_name = ?,
          gender = ?,
          date_of_birth = ?,
          relationship = ?,
          is_primary = ?,
          cart_id = ?,
          updated_at = now()
        WHERE id = ? AND customer_id = ? AND deleted_at IS NULL
        RETURNING *
      `, [
        input.first_name ?? existing.first_name,
        input.last_name ?? existing.last_name,
        input.gender ?? existing.gender,
        input.date_of_birth ? input.date_of_birth : existing.date_of_birth,
        next_relationship,
        next_is_primary,
        input.cart_id ?? existing.cart_id,
        id,
        customer_id
      ]);

      return result[0] as TravellerProfile;
    });
  }

  async delete(customer_id: string, id: string): Promise<void> {
    await this.manager.transactional(async (tx) => {
      await tx.execute(`
        UPDATE ${TABLE} SET
          deleted_at = now(),
          updated_at = now()
        WHERE id = ? AND customer_id = ? AND deleted_at IS NULL
      `, [id, customer_id]);
    });
  }
}
