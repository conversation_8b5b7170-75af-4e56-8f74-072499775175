import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;

    if (!cart_id) {
      return res.status(400).json({ message: "cart id is required" });
    }

    // Resolve our CustomerTravellerService via module key registered in src/modules/index.ts
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as {
      listByCart: (cart_id: string) => Promise<any[]>;
    };

    // Fallback: if service isn't registered for some reason, return empty list gracefully
    if (!travellerService?.listByCart) {
      return res.status(200).json({
        cart_id,
        profiles: [],
      });
    }

    const profiles = await travellerService.listByCart(cart_id);

    return res.status(200).json({
      cart_id,
      profiles,
    });
  } catch (error) {
    // Map MedusaError style objects if present
    const message =
      (error as any)?.message ||
      (typeof error === "string" ? error : "Internal server error");

    console.error("Error in traveller profiles list:", error);
    return res.status(500).json({
      message,
    });
  }
};
