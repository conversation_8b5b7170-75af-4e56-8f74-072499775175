import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../../modules/customer-travellers";

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const traveller_id = req.params.traveller_id;

    if (!cart_id) {
      return res.status(400).json({ message: "cart id is required" });
    }
    if (!traveller_id) {
      return res.status(400).json({ message: "traveller id is required" });
    }

    // Resolve service
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as any;

    if (!travellerService?.delete) {
      return res.status(500).json({ message: "Traveller service not available" });
    }


    // Optional: ensure the traveller is associated with this cart (if such a method exists).
    // Since service requires customer scoping, we skip ownership checks here and rely on cart context only.
    // If needed, you can add a repository lookup by traveller_id and cart_id here in the future.

    // Service currently requires customer_id to delete; since requirement is "Not need customer_id",
    // perform a cart-scoped soft-delete using a direct query via the service's manager if exposed,
    // otherwise attempt a generic deleteById if available.
    if (typeof travellerService.deleteById === "function") {
      await travellerService.deleteById(traveller_id);
    } else if (travellerService?.manager?.execute) {
      await travellerService.manager.execute(
        `
        UPDATE customer_traveller
        SET deleted_at = now(), updated_at = now()
        WHERE id = ? AND deleted_at IS NULL
        `,
        [traveller_id]
      );
    } else {
      // As a last resort, try calling delete with only id (if service is overloaded)
      await travellerService.delete?.(traveller_id);
    }

    return res.status(204).send();
  } catch (error) {
    const message =
      (error as any)?.message ||
      (typeof error === "string" ? error : "Internal server error");

    console.error("Error deleting traveller profile:", error);
    return res.status(500).json({
      message,
    });
  }
};
