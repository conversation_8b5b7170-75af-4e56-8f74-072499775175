import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const traveller_id = req.params.traveller_id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // TODO: Implement proper database access once customer_traveller entity is registered
    // For now, return a mock success response to indicate the endpoint is working
    console.log(`Mock delete: customer_id=${customer_id}, traveller_id=${traveller_id}, cart_id=${cart_id}`);

    res.status(204).send();
  } catch (error) {
    console.error("Error deleting traveller profile:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
