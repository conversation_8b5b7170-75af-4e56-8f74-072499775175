import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateTravellerProfileSchema } from "../schemas";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;

    if (!cart_id) {
      return res.status(400).json({ message: "cart id is required" });
    }

    // Validate request body using schema
    const validation = CreateTravellerProfileSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        message: "Validation failed",
        errors: validation.error.errors,
      });
    }
    const body = validation.data;

    // Resolve service and create profile
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as {
      create: (input: {
        customer_id: string;
        cart_id?: string | null;
        first_name: string;
        last_name: string;
        date_of_birth: string;
        gender?: "male" | "female" | "other";
        relationship?: string;
        is_primary?: boolean;
      }) => Promise<any>;
    };

    if (!travellerService?.create) {
      return res.status(500).json({ message: "Traveller service not available" });
    }

    // Resolve customer id from known locations (avoid TS errors by using index access)
    const anyReq = req as any;
    const customer_id =
      anyReq?.user?.customer_id ||
      anyReq?.auth?.customer_id ||
      (req.body as any)?.customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "customer authentication required" });
    }

    const created = await travellerService.create({
      customer_id,
      cart_id,
      first_name: body.first_name,
      last_name: body.last_name,
      date_of_birth: body.date_of_birth,
      gender: body.gender,
      relationship: body.relationship as any,
      is_primary: body.is_primary,
    });

    return res.status(201).json({ cart_id, profile: created });
  } catch (error) {
    const message =
      (error as any)?.message ||
      (typeof error === "string" ? error : "Internal server error");

    console.error("Error creating traveller profile:", error);
    return res.status(500).json({
      message,
    });
  }
};
