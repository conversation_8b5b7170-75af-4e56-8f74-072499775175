import { z } from "zod";

/**
 * Schema for creating a traveller profile
 */
export const CreateTravellerProfileSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  customer_id: z.string().min(1, "Customer ID is required"),
  date_of_birth: z.string().min(1, "Date of birth is required"),
  gender: z.enum(["male", "female", "other"]).optional(),
  relationship: z.string().optional(),
  is_primary: z.boolean().default(false),
}).refine((data) => {
  // If not primary, relationship is required
  if (data.is_primary === false && !data.relationship) {
    return false;
  }
  return true;
}, {
  message: "relationship is required for non-primary travellers",
  path: ["relationship"],
});

/**
 * Schema for updating a traveller profile
 */
export const UpdateTravellerProfileSchema = z.object({
  first_name: z.string().min(1).optional(),
  last_name: z.string().min(1).optional(),
  date_of_birth: z.string().min(1).optional(),
  gender: z.enum(["male", "female", "other"]).optional(),
  relationship: z.string().optional(),
  is_primary: z.boolean().optional(),
  cart_id: z.string().optional(),
}).refine((data) => {
  // If explicitly setting non-primary, relationship should be present
  if (data.is_primary === false && !data.relationship) {
    return false;
  }
  return true;
}, {
  message: "relationship is required for non-primary travellers",
  path: ["relationship"],
});

/**
 * Type definitions for request bodies
 */
export type CreateTravellerProfileReq = z.infer<typeof CreateTravellerProfileSchema>;
export type UpdateTravellerProfileReq = z.infer<typeof UpdateTravellerProfileSchema>;
